-- Estensione necessarie
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- <PERSON>bella dei profili utente
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  avatar_url TEXT,
  featured_badge_id UUID,
  preferences JSONB DEFAULT '{"travels_with_pets": false, "travels_with_family": false, "has_disability_needs": false, "has_health_conditions": false, "preferred_categories": [], "preferred_activities": []}',
  achievement_progress JSONB DEFAULT '{}',
  notification_settings JSONB DEFAULT '{"general_notifications": true, "event_notifications": true, "weather_alerts": true, "poi_suggestions": true, "achievement_notifications": true, "personal_notifications": true}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>bella dei badge
CREATE TABLE IF NOT EXISTS public.badges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  icon_url TEXT NOT NULL,
  category TEXT NOT NULL,
  requirements TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabella dei badge ottenuti dagli utenti
CREATE TABLE IF NOT EXISTS public.user_badges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  badge_id UUID NOT NULL REFERENCES public.badges(id) ON DELETE CASCADE,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_featured BOOLEAN DEFAULT FALSE,
  UNIQUE(user_id, badge_id)
);

-- Tabella dei POI (Points of Interest)
CREATE TABLE IF NOT EXISTS public.pois (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL,
  type TEXT NOT NULL,
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  images_urls JSONB DEFAULT '[]',
  accessibility_info TEXT,
  opening_hours TEXT,
  price_info TEXT,
  booking_required BOOLEAN DEFAULT FALSE,
  contact_info TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabella delle interazioni degli utenti
CREATE TABLE IF NOT EXISTS public.user_interactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  poi_id UUID REFERENCES public.pois(id) ON DELETE CASCADE,
  achievement_id TEXT,
  event_id UUID,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  details JSONB DEFAULT '{}',
  location JSONB
);

-- Tabella eventi calendario
CREATE TABLE IF NOT EXISTS public.calendar_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  location JSONB,
  poi_id UUID REFERENCES public.pois(id) ON DELETE SET NULL,
  is_all_day BOOLEAN DEFAULT FALSE,
  category TEXT NOT NULL,
  reminder_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabella dati meteo
CREATE TABLE IF NOT EXISTS public.weather_data (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  temperature DOUBLE PRECISION NOT NULL,
  condition TEXT NOT NULL,
  wind_speed DOUBLE PRECISION NOT NULL,
  wind_direction TEXT NOT NULL,
  humidity DOUBLE PRECISION NOT NULL,
  precipitation DOUBLE PRECISION NOT NULL,
  forecast JSONB DEFAULT '[]',
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  location JSONB NOT NULL
);

-- Tabella notifiche
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_read BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  poi_id UUID REFERENCES public.pois(id) ON DELETE SET NULL,
  event_id UUID REFERENCES public.calendar_events(id) ON DELETE SET NULL,
  badge_id UUID REFERENCES public.badges(id) ON DELETE SET NULL
);

-- Trigger per aggiornare updated_at quando un POI viene modificato
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pois_updated_at
BEFORE UPDATE ON public.pois
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Funzione per verificare e assegnare achievement quando un utente visita un POI
CREATE OR REPLACE FUNCTION check_and_assign_achievements()
RETURNS TRIGGER AS $$
DECLARE
  achievement_key TEXT;
  badge_id UUID;
  user_achievement_progress JSONB;
BEGIN
  -- Ottieni il progresso attuale dell'utente
  SELECT achievement_progress INTO user_achievement_progress
  FROM public.user_profiles
  WHERE id = NEW.user_id;

  -- Se l'interazione è una visita a un POI
  IF NEW.type = 'visit' THEN
    -- Ottieni la categoria del POI
    DECLARE
      poi_category TEXT;
    BEGIN
      SELECT category INTO poi_category
      FROM public.pois
      WHERE id = NEW.poi_id;

      -- Logica per assegnare gli achievement in base alla categoria
      IF poi_category = 'beach' THEN
        -- Controlla se l'utente ha visitato tutte le spiagge
        DECLARE
          beach_count INTEGER;
          user_beach_visits INTEGER;
        BEGIN
          SELECT COUNT(*) INTO beach_count
          FROM public.pois
          WHERE category = 'beach';

          SELECT COUNT(DISTINCT poi_id) INTO user_beach_visits
          FROM public.user_interactions
          WHERE user_id = NEW.user_id
            AND type = 'visit'
            AND poi_id IN (SELECT id FROM public.pois WHERE category = 'beach');

          IF user_beach_visits >= beach_count THEN
            -- Aggiorna il progresso dell'utente
            user_achievement_progress = jsonb_set(
              COALESCE(user_achievement_progress, '{}'::jsonb),
              '{all_beaches}',
              'true'::jsonb
            );

            -- Cerca il badge corrispondente
            SELECT id INTO badge_id
            FROM public.badges
            WHERE requirements = 'all_beaches'
            LIMIT 1;

            -- Se il badge esiste e l'utente non lo ha già
            IF badge_id IS NOT NULL AND NOT EXISTS (
              SELECT 1 FROM public.user_badges
              WHERE user_id = NEW.user_id AND badge_id = badge_id
            ) THEN
              -- Assegna il badge all'utente
              INSERT INTO public.user_badges (user_id, badge_id)
              VALUES (NEW.user_id, badge_id);

              -- Crea una notifica
              INSERT INTO public.notifications (
                user_id, title, message, type, badge_id
              ) VALUES (
                NEW.user_id,
                'Nuovo Badge Sbloccato!',
                'Hai visitato tutte le spiagge dell''isola!',
                'achievement',
                badge_id
              );
            END IF;
          END IF;
        END;
      END IF;

      -- Altre logiche per altre categorie...

    END;
  END IF;

  -- Gestione badge basati sulla posizione
  IF NEW.type = 'location_enter' AND NEW.details->>'context' = 'on_island' THEN
    -- Badge "Island Access" - primo accesso all'isola
    SELECT id INTO badge_id
    FROM public.badges
    WHERE requirements = 'island_access'
    LIMIT 1;

    IF badge_id IS NOT NULL AND NOT EXISTS (
      SELECT 1 FROM public.user_badges
      WHERE user_id = NEW.user_id AND badge_id = badge_id
    ) THEN
      INSERT INTO public.user_badges (user_id, badge_id)
      VALUES (NEW.user_id, badge_id);

      INSERT INTO public.notifications (
        user_id, title, message, type, badge_id
      ) VALUES (
        NEW.user_id,
        'Benvenuto a Tavolara!',
        'Hai sbloccato il badge Island Access!',
        'achievement',
        badge_id
      );
    END IF;

    -- Badge "Island Fan" - seconda visita nella stessa settimana
    IF EXISTS (
      SELECT 1 FROM public.user_interactions
      WHERE user_id = NEW.user_id
        AND type = 'location_enter'
        AND details->>'context' = 'on_island'
        AND timestamp >= NOW() - INTERVAL '7 days'
        AND timestamp < NEW.timestamp
    ) THEN
      SELECT id INTO badge_id
      FROM public.badges
      WHERE requirements = 'island_fan'
      LIMIT 1;

      IF badge_id IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM public.user_badges
        WHERE user_id = NEW.user_id AND badge_id = badge_id
      ) THEN
        INSERT INTO public.user_badges (user_id, badge_id)
        VALUES (NEW.user_id, badge_id);

        INSERT INTO public.notifications (
          user_id, title, message, type, badge_id
        ) VALUES (
          NEW.user_id,
          'Island Fan!',
          'Hai visitato l''isola due volte questa settimana!',
          'achievement',
          badge_id
        );
      END IF;
    END IF;
  END IF;

  -- Aggiorna il progresso dell'achievement dell'utente
  UPDATE public.user_profiles
  SET achievement_progress = user_achievement_progress
  WHERE id = NEW.user_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_achievements_on_interaction
AFTER INSERT ON public.user_interactions
FOR EACH ROW
EXECUTE FUNCTION check_and_assign_achievements();

-- Funzione per aggiornare la tabella featured_badge quando un utente imposta un badge come in evidenza
CREATE OR REPLACE FUNCTION update_featured_badge()
RETURNS TRIGGER AS $$
BEGIN
  -- Se un badge è impostato come in evidenza, assicurati che gli altri non lo siano
  IF NEW.is_featured = TRUE THEN
    -- Aggiorna il profilo utente
    UPDATE public.user_profiles
    SET featured_badge_id = NEW.id
    WHERE id = NEW.user_id;

    -- Imposta tutti gli altri badge dell'utente come non in evidenza
    UPDATE public.user_badges
    SET is_featured = FALSE
    WHERE user_id = NEW.user_id
      AND id != NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_featured_badge_on_change
AFTER UPDATE OF is_featured ON public.user_badges
FOR EACH ROW
WHEN (NEW.is_featured = TRUE)
EXECUTE FUNCTION update_featured_badge();

-- Inserimento dati di esempio per i badge
INSERT INTO public.badges (name, description, icon_url, category, requirements) VALUES
('Esploratore Novizio', 'Primo badge per aver visitato un POI', 'https://example.com/badge1.png', 'explorer', 'visit_first_poi'),
('Re di Tavolara', 'Badge per aver visitato tutti i punti di interesse principali', 'https://example.com/badge2.png', 'collection', 'visit_all_main_pois'),
('Amante delle Spiagge', 'Badge per aver visitato tutte le spiagge', 'https://example.com/badge3.png', 'collection', 'all_beaches'),
('Scalatore', 'Badge per aver raggiunto la vetta', 'https://example.com/badge4.png', 'activity', 'reach_summit'),
('Island Access', 'Badge per aver raggiunto fisicamente l''isola di Tavolara', 'https://example.com/island_access.png', 'location', 'island_access'),
('Island Fan', 'Badge per aver visitato l''isola due volte nella stessa settimana', 'https://example.com/island_fan.png', 'location', 'island_fan');

-- Inserimento dati di esempio per i POI off-island
INSERT INTO public.pois (name, description, category, type, latitude, longitude, opening_hours, contact_info) VALUES
('Porto di Olbia', 'Punto di partenza principale per Tavolara', 'boarding', 'dock', 40.9237, 9.5026, '06:00-20:00', '+39 0789 123456'),
('Parcheggio Porto Olbia', 'Parcheggio vicino al porto di partenza', 'parking', 'service', 40.9240, 9.5020, '24/7', NULL),
('Noleggio Barche Marina', 'Noleggio imbarcazioni per Tavolara', 'rental', 'service', 40.9235, 9.5030, '08:00-19:00', '+39 0789 654321'),
('Escursioni Tavolara Tours', 'Tour organizzati all''isola', 'excursion', 'activity', 40.9230, 9.5025, '09:00-18:00', '+39 0789 987654'),
('Centro Immersioni Sardegna', 'Immersioni guidate a Tavolara', 'diving', 'activity', 40.9245, 9.5035, '08:30-17:30', '+39 0789 456789'),
('Biglietteria Traghetti', 'Vendita biglietti per l''isola', 'ticket_office', 'service', 40.9238, 9.5028, '07:00-19:00', '+39 0789 321654');