import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Alert,
  Modal,
  Platform,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { UserProfile, UserPreferences, NotificationSettings } from '../types/auth';
import { UserBadge } from '../types/badge';
import { ACHIEVEMENT_KEYS } from '../types/badge';
import { Notification } from '../types/interaction';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';

// Componenti
import ProfileHeader from '../components/ProfileHeader';
import PreferencesSection from '../components/PreferencesSection';
import AchievementsSection from '../components/AchievementsSection';
import NotificationsSection from '../components/NotificationsSection';

export default function ProfileScreen({ navigation }) {
  const { user, signOut } = useAuth();
  const [loading, setLoading] = useState(true);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [featuredBadge, setFeaturedBadge] = useState<UserBadge | null>(null);
  const [userBadges, setUserBadges] = useState<UserBadge[]>([]);
  const [preferences, setPreferences] = useState<UserPreferences>({
    travels_with_pets: false,
    travels_with_family: false,
    has_disability_needs: false,
    has_health_conditions: false,
    preferred_categories: [],
    preferred_activities: [],
  });
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    general_notifications: true,
    event_notifications: true,
    weather_alerts: true,
    poi_suggestions: true,
    achievement_notifications: true,
    personal_notifications: true,
  });
  const [achievementProgress, setAchievementProgress] = useState<Record<string, boolean>>({});
  const [recentNotifications, setRecentNotifications] = useState<Notification[]>([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showActivityModal, setShowActivityModal] = useState(false);

  useEffect(() => {
    if (user) {
      fetchUserProfile();
      fetchUserBadges();
      fetchAchievementProgress();
      fetchRecentNotifications();
    }
  }, [user]);

  const fetchUserProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user?.id)
        .single();

      if (error) throw error;
      
      if (data) {
        // Imposta preferenze utente
        if (data.preferences) {
          setPreferences(data.preferences);
        }
        
        // Imposta impostazioni notifiche
        if (data.notification_settings) {
          setNotificationSettings(data.notification_settings);
        }
        
        // Se c'è un badge in evidenza, caricalo
        if (data.featured_badge_id) {
          fetchFeaturedBadge(data.featured_badge_id);
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      Alert.alert('Errore', 'Impossibile caricare il profilo');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserBadges = async () => {
    try {
      const { data, error } = await supabase
        .from('user_badges')
        .select(`
          *,
          badge:badges (*)
        `)
        .eq('user_id', user?.id)
        .order('earned_at', { ascending: false });

      if (error) throw error;
      
      if (data) {
        setUserBadges(data);
      }
    } catch (error) {
      console.error('Error fetching user badges:', error);
    }
  };

  const fetchFeaturedBadge = async (badgeId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_badges')
        .select(`
          *,
          badge:badges (*)
        `)
        .eq('id', badgeId)
        .single();

      if (error) throw error;
      
      if (data) {
        setFeaturedBadge(data);
      }
    } catch (error) {
      console.error('Error fetching featured badge:', error);
    }
  };

  const fetchAchievementProgress = async () => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('achievement_progress')
        .eq('id', user?.id)
        .single();

      if (error) throw error;
      
      if (data?.achievement_progress) {
        setAchievementProgress(data.achievement_progress);
      } else {
        // Se non ci sono achievement, crea un oggetto vuoto con tutti gli achievement non completati
        const defaultProgress = {};
        Object.values(ACHIEVEMENT_KEYS).forEach(key => {
          defaultProgress[key] = false;
        });
        setAchievementProgress(defaultProgress);
      }
    } catch (error) {
      console.error('Error fetching achievement progress:', error);
    }
  };

  const fetchRecentNotifications = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user?.id)
        .order('timestamp', { ascending: false })
        .limit(5);

      if (error) throw error;
      
      if (data) {
        setRecentNotifications(data);
      }
    } catch (error) {
      console.error('Error fetching recent notifications:', error);
    }
  };

  const updatePreference = async (key: keyof UserPreferences, value: any) => {
    try {
      const newPreferences = { ...preferences, [key]: value };
      
      await supabase
        .from('user_profiles')
        .update({ preferences: newPreferences })
        .eq('id', user?.id);

      setPreferences(newPreferences);
    } catch (error) {
      console.error('Error updating preferences:', error);
      Alert.alert('Errore', 'Impossibile aggiornare le preferenze');
    }
  };

  const updateNotificationSetting = async (key: keyof NotificationSettings, value: boolean) => {
    try {
      const newSettings = { ...notificationSettings, [key]: value };
      
      await supabase
        .from('user_profiles')
        .update({ notification_settings: newSettings })
        .eq('id', user?.id);

      setNotificationSettings(newSettings);
    } catch (error) {
      console.error('Error updating notification settings:', error);
      Alert.alert('Errore', 'Impossibile aggiornare le impostazioni delle notifiche');
    }
  };

  const handleAvatarPress = async () => {
    // Chiedi permessi per accedere alla libreria immagini
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permesso negato', 'Abbiamo bisogno dei permessi per accedere alle tue foto');
        return;
      }
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.cancelled && result.uri) {
        await uploadAvatar(result.uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Errore', 'Impossibile selezionare l\'immagine');
    }
  };

  const uploadAvatar = async (uri: string) => {
    try {
      setUploadingImage(true);

      // Converti l'URI in un file (Blob)
      const response = await fetch(uri);
      const blob = await response.blob();
      
      // Nome file univoco
      const fileExt = uri.split('.').pop();
      const fileName = `${user?.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Carica l'immagine su Storage
      const { error: uploadError } = await supabase.storage
        .from('user-content')
        .upload(filePath, blob);

      if (uploadError) throw uploadError;

      // Ottieni l'URL pubblico
      const { data: urlData } = supabase.storage
        .from('user-content')
        .getPublicUrl(filePath);

      // Aggiorna il profilo utente con il nuovo URL dell'avatar
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ avatar_url: urlData.publicUrl })
        .eq('id', user?.id);

      if (updateError) throw updateError;

      // Ricarica il profilo utente
      fetchUserProfile();

      Alert.alert('Successo', 'Immagine profilo aggiornata');

    } catch (error) {
      console.error('Error uploading avatar:', error);
      Alert.alert('Errore', 'Impossibile caricare l\'immagine');
    } finally {
      setUploadingImage(false);
    }
  };

  const handleBadgePress = (badge: UserBadge) => {
    // Imposta il badge selezionato come badge in evidenza
    setFeaturedBadgeas(badge.id);
  };

  const setFeaturedBadgeas = async (badgeId: string) => {
    try {
      await supabase
        .from('user_profiles')
        .update({ featured_badge_id: badgeId })
        .eq('id', user?.id);

      // Ricarica il badge in evidenza
      fetchFeaturedBadge(badgeId);
      
      Alert.alert('Successo', 'Badge in evidenza aggiornato');
    } catch (error) {
      console.error('Error setting featured badge:', error);
      Alert.alert('Errore', 'Impossibile impostare il badge in evidenza');
    }
  };

  const handleNotificationPress = async (notification: Notification) => {
    // Segna la notifica come letta
    try {
      await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notification.id);

      // Se la notifica ha un'azione (ad es. navigare a un POI)
      if (notification.poi_id) {
        navigation.navigate('POIDetail', { poiId: notification.poi_id });
      } else if (notification.event_id) {
        navigation.navigate('EventDetail', { eventId: notification.event_id });
      } else if (notification.badge_id) {
        // Mostra dettagli del badge
        const selectedBadge = userBadges.find(b => b.badge?.id === notification.badge_id);
        if (selectedBadge) {
          // Qui potremmo mostrare un modale con i dettagli del badge
          Alert.alert(
            selectedBadge.badge?.name || 'Badge',
            selectedBadge.badge?.description || 'Hai sbloccato questo badge!'
          );
        }
      }

      // Ricarica le notifiche
      fetchRecentNotifications();
    } catch (error) {
      console.error('Error handling notification:', error);
    }
  };

  // Funzioni per i modali
  const handleManageCategories = () => {
    setShowCategoryModal(true);
  };

  const handleManageActivities = () => {
    setShowActivityModal(true);
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#3498db" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView>
        {uploadingImage && (
          <View style={styles.uploadingOverlay}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.uploadingText}>Caricamento immagine...</Text>
          </View>
        )}

        <ProfileHeader 
          userProfile={user as UserProfile}
          featuredBadge={featuredBadge}
          onAvatarPress={handleAvatarPress}
          onBadgePress={() => navigation.navigate('BadgesScreen')}
        />

        <PreferencesSection 
          preferences={preferences}
          onPreferenceChange={updatePreference}
          onManageCategories={handleManageCategories}
          onManageActivities={handleManageActivities}
        />

        <AchievementsSection 
          userBadges={userBadges}
          achievementProgress={achievementProgress}
          onBadgePress={handleBadgePress}
          onViewAllPress={() => navigation.navigate('AchievementsScreen')}
        />

        <NotificationsSection 
          settings={notificationSettings}
          recentNotifications={recentNotifications}
          onSettingChange={updateNotificationSetting}
          onNotificationPress={handleNotificationPress}
          onViewAllPress={() => navigation.navigate('NotificationsScreen')}
        />

        <TouchableOpacity style={styles.signOutButton} onPress={signOut}>
          <Text style={styles.signOutText}>Esci</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Modali per gestire categorie e attività preferite */}
      {/* Questi sarebbero componenti separati in un'implementazione completa */}
      <Modal
        visible={showCategoryModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Gestisci Categorie Preferite</Text>
            {/* Qui andrà la lista delle categorie selezionabili */}
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowCategoryModal(false)}
            >
              <Text style={styles.closeButtonText}>Chiudi</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal
        visible={showActivityModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowActivityModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Gestisci Attività Preferite</Text>
            {/* Qui andrà la lista delle attività selezionabili */}
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowActivityModal(false)}
            >
              <Text style={styles.closeButtonText}>Chiudi</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  uploadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  signOutButton: {
    backgroundColor: '#e74c3c',
    padding: 15,
    margin: 15,
    marginTop: 5,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  signOutText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  closeButton: {
    marginTop: 20,
    backgroundColor: '#3498db',
    padding: 10,
    borderRadius: 5,
    width: '100%',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});